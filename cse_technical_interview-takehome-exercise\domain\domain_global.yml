version: "3.1"

slots:
  customer_id:
    type: text
    mappings:
      - type: from_llm
    initial_value: 125 # this is manually set here as we usually get this from the an API call

  customer_first_name:
    type: text
    mappings:
      - type: custom
        action: action_get_customer_info

responses:
  utter_session_start:
    - text: |
        Hey {customer_first_name} 👋. I am your virtual AI assistant that can help you with:
        - credit card purchase
        - balance check
        How may I help you, today?

  utter_end_conversation:
    - text: |
        I hope I was helpful today!
        Let me know if you need more assistance.

  utter_fallback:
    - text: |
        I appreciate your question!
        That's a bit outside my expertise, but I'm here to help buy a new credit card or check your balance.
        Would you like assistance with that?

  utter_bot_challenge:
    - text: |
        I'm an AI assistant designed to assist you quickly and efficiently.
        If you ever need to speak with a human agent, just let me know!

actions:
  - action_get_customer_info
  - action_session_start
  - utter_session_start
  - utter_bot_challenge
  - utter_more_help_needed_handoff_pattern
