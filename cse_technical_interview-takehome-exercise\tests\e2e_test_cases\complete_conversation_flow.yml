fixtures:
  - customer_information: # name of the fixture must be provided and be unique
      - customer_id: "125" # Edith Piaf with account EF1234 and balance 300

test_cases:
  - test_case: complete conversation flow - balance check then credit card purchase
    steps:
    # Initial balance check with wrong account number
    - user: "check my balance"
      assertions:
        - flow_started: "check_balance"
        - bot_uttered:
            utter_name: utter_ask_account_id_from_user
    - user: "AB1234"
      assertions:
        - slot_was_set:
            - name: "account_id_from_user"
              value: "AB1234"
        - action_executed: action_get_data_from_db
        - slot_was_set:
          - name: "check_access_status"
            value: False
        - bot_uttered:
            utter_name: utter_dont_have_access
        - bot_uttered:
            utter_name: utter_ask_provide_account_id
    # User agrees to get account ID
    - user: "Yes" 
      assertions:
        - slot_was_set:
            - name: "provide_account_id"
              value: True
        - bot_uttered:
            utter_name: utter_provide_account_id_from_db
    # User asks to check balance again
    - user: "check my balance then"
      assertions:
        - flow_started: "check_balance"
        - bot_uttered:
            utter_name: utter_ask_account_id_from_user
    # User provides correct account number
    - user: "EF1234"
      assertions:
        - slot_was_set:
            - name: "account_id_from_user"
              value: "EF1234"
        - action_executed: action_get_data_from_db
        - slot_was_set:
          - name: "check_access_status"
            value: True
        - bot_uttered:
            utter_name: utter_have_access
        - bot_uttered:
            utter_name: utter_provide_balance
    # User asks to buy credit card
    - user: "can you help me buy a credit card now"
      assertions:
        - flow_started: "credit_card_purchase"
        - bot_uttered:
            utter_name: utter_ask_credit_card_type
    # User selects Visa
    - user: "Visa"
      assertions:
        - slot_was_set:
            - name: "credit_card_type"
              value: "Visa"
        - bot_uttered:
            utter_name: utter_ask_delivery_type
    # User selects home delivery
    - user: "at home"
      assertions:
        - slot_was_set:
            - name: "delivery_type"
              value: "Home"
        - bot_uttered:
            utter_name: utter_ask_address
    # User provides address
    - user: "12, street Jeanne, Paris France"
      assertions:
        - slot_was_set:
            - name: "address"
              value: "12, street Jeanne, Paris France"
        - action_executed: action_recap_information
    # User says No to confirmation
    - user: "No"
      assertions:
        - slot_was_set:
            - name: "confirm_recap"
              value: False
        - bot_uttered:
            utter_name: utter_transfer_to_human
