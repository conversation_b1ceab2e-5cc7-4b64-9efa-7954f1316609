flows:
  check_balance:
    description: check the balance of the user's account
    steps:
      - collect: account_id_from_user
        ask_before_filling: True
      - action: action_get_data_from_db
        next:
          - if: slots.check_access_status
            then:
              - action: utter_have_access
              - action: utter_provide_balance
                next: "END"
          - else:
            - action: utter_dont_have_access
            - collect: provide_account_id
              ask_before_filling: True
              description: |
                This slot is boolean variable. it holds one of two possible values: true or false
                (provide_account_id = True, provide_account_id = False), and if the user says "Yes," it should be
                considered True, while "No" should be considered False.
              next:
                - if: slots.provide_account_id
                  then:
                    - action: action_get_customer_info
                    - action: utter_provide_account_id_from_db
                      next: "END"
                - else:
                  - action: utter_end_conversation
                    next: "END"