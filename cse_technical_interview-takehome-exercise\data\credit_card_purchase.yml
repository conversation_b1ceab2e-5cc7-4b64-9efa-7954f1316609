flows:
  credit_card_purchase:
    description: Help user purchase a credit card with type selection, delivery options, and address collection
    steps:
      - collect: credit_card_type
        ask_before_filling: True
      - collect: delivery_type
        ask_before_filling: True
        next:
          - if: slots.delivery_type == "Home"
            then:
              - collect: address
                ask_before_filling: True
              - action: action_recap_information
              - collect: confirm_recap
                ask_before_filling: False
                next:
                  - if: slots.confirm_recap == True
                    then:
                      - action: action_process_credit_card_purchase
                        next: "END"
                  - else:
                    - action: utter_transfer_to_human
                      next: "END"
          - else:
            - action: action_recap_information
            - collect: confirm_recap
              ask_before_filling: False
              next:
                - if: slots.confirm_recap == True
                  then:
                    - action: action_process_credit_card_purchase
                      next: "END"
                - else:
                  - action: utter_transfer_to_human
                    next: "END"
