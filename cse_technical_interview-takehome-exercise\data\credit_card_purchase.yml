flows:
  credit_card_purchase:
    description: Help user purchase a credit card with type selection, delivery options, and address collection
    steps:
      - collect: credit_card_type
        ask_before_filling: True
      - collect: delivery_type
        ask_before_filling: True
        next:
          - if: slots.delivery_type == "Home"
            then:
              - collect: address
                ask_before_filling: True
              - action: action_recap_information
              - collect: confirm_recap
                ask_before_filling: False
                description: |
                  This slot is boolean variable. it holds one of two possible values: true or false
                  (confirm_recap = True, confirm_recap = False), and if the user says "Yes," it should be
                  considered True, while "No" should be considered False.
                next:
                  - if: slots.confirm_recap
                    then:
                      - action: action_process_credit_card_purchase
                        next: "END"
                  - else:
                    - link: pattern_human_handoff
          - else:
            - action: action_recap_information
            - collect: confirm_recap
              ask_before_filling: False
              description: |
                This slot is boolean variable. it holds one of two possible values: true or false
                (confirm_recap = True, confirm_recap = False), and if the user says "Yes," it should be
                considered True, while "No" should be considered False.
              next:
                - if: slots.confirm_recap
                  then:
                    - action: action_process_credit_card_purchase
                      next: "END"
                - else:
                  - link: pattern_human_handoff
