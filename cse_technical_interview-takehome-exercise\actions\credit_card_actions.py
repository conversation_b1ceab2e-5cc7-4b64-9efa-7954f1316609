from typing import Any, Text, Dict, List
from rasa_sdk import Action, Tracker
from rasa_sdk.events import SlotSet
from rasa_sdk.executor import CollectingDispatcher


class ActionProcessCreditCardPurchase(Action):
    def name(self) -> Text:
        return "action_process_credit_card_purchase"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        # Get the collected information
        credit_card_type = tracker.get_slot("credit_card_type")
        delivery_type = tracker.get_slot("delivery_type")
        address = tracker.get_slot("address")
        
        # In a real implementation, this would process the credit card purchase
        # For now, we'll just confirm the purchase
        
        if delivery_type == "Home" and address:
            message = f"Great! Your {credit_card_type} credit card purchase has been processed. It will be delivered to: {address}"
        else:
            message = f"Great! Your {credit_card_type} credit card purchase has been processed. It will be delivered to the closest bank near you."
        
        dispatcher.utter_message(text=message)
        
        # Clear the slots for next interaction
        return [
            SlotSet("credit_card_type", None),
            SlotSet("delivery_type", None),
            SlotSet("address", None),
            SlotSet("confirm_recap", None)
        ]


class ActionRecapInformation(Action):
    def name(self) -> Text:
        return "action_recap_information"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        credit_card_type = tracker.get_slot("credit_card_type")
        delivery_type = tracker.get_slot("delivery_type")
        address = tracker.get_slot("address")

        # Create the recap message based on delivery type
        if delivery_type == "Home":
            recap_text = f"""Let's make sure all information provided are in order
Credit card type : {credit_card_type}
Delivery type: Home
Home address : {address}
Can you confirm that all the information captured above is accurate:"""
        else:
            recap_text = f"""Let's make sure all information provided are in order
Credit card type : {credit_card_type}
Delivery type: to the closest bank near you
Can you confirm that all the information captured above is accurate:"""

        # Send the recap with buttons
        buttons = [
            {"title": "Yes", "payload": "/SetSlots(confirm_recap=True)"},
            {"title": "No", "payload": "/SetSlots(confirm_recap=False)"}
        ]

        dispatcher.utter_message(text=recap_text, buttons=buttons)

        return []
