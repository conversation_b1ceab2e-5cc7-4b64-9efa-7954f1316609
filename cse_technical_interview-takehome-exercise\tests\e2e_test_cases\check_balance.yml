fixtures:
  - customer_information: # name of the fixture must be provided and be unique
      - customer_id: "125" # every fixture can contain multiple slot key-value pairs
                          # This will be helpful when to se user information

test_cases:
  - test_case: check balance wrong id
    steps:
    - user: "I need to check my balance"
      assertions:
        - flow_started: "check_balance"
        - bot_uttered:
            utter_name: utter_ask_account_id_from_user
    - user: BA18291
      assertions:
        - slot_was_set:
            - name: "account_id_from_user"
              value: "BA18291"
        - action_executed: action_get_data_from_db
        - slot_was_set:
          - name: "check_access_status"
            value: False
        - bot_uttered:
            utter_name: utter_dont_have_access
        - bot_uttered:
            utter_name: utter_ask_provide_account_id
    - user: "Yes" 
      assertions:
        - bot_uttered:
            utter_name: utter_provide_account_id_from_db
# ====================================================>
  - test_case: check balance right id
    steps:
    - user: "I would like to check my balance"
      assertions:
        - flow_started: "check_balance"
        - bot_uttered:
            utter_name: utter_ask_account_id_from_user
    - user: EF1234
      assertions:
        - slot_was_set:
            - name: "account_id_from_user"
              value: "EF1234"
        - action_executed: action_get_data_from_db
        - slot_was_set:
          - name: "check_access_status"
            value: True
        - bot_uttered:
            utter_name: utter_have_access
        - bot_uttered:
            utter_name: utter_provide_balance

# ====================================================>
  - test_case: credit card purchase flow with home delivery
    steps:
    - user: "can you help me buy a credit card now"
      assertions:
        - flow_started: "credit_card_purchase"
        - bot_uttered:
            utter_name: utter_ask_credit_card_type
    - user: "Visa"
      assertions:
        - slot_was_set:
            - name: "credit_card_type"
              value: "Visa"
        - bot_uttered:
            utter_name: utter_ask_delivery_type
    - user: "at home"
      assertions:
        - slot_was_set:
            - name: "delivery_type"
              value: "Home"
        - bot_uttered:
            utter_name: utter_ask_address
    - user: "12, street Jeanne, Paris France"
      assertions:
        - slot_was_set:
            - name: "address"
              value: "12, street Jeanne, Paris France"
        - action_executed: action_recap_information
    - user: "No"
      assertions:
        - slot_was_set:
            - name: "confirm_recap"
              value: False
        - bot_uttered:
            utter_name: utter_transfer_to_human

# ====================================================>
  - test_case: credit card purchase flow with bank delivery
    steps:
    - user: "I want to buy a credit card"
      assertions:
        - flow_started: "credit_card_purchase"
        - bot_uttered:
            utter_name: utter_ask_credit_card_type
    - user: "Mastercard"
      assertions:
        - slot_was_set:
            - name: "credit_card_type"
              value: "Mastercard"
        - bot_uttered:
            utter_name: utter_ask_delivery_type
    - user: "to the closest bank near you"
      assertions:
        - slot_was_set:
            - name: "delivery_type"
              value: "Bank"
        - action_executed: action_recap_information
    - user: "Yes"
      assertions:
        - slot_was_set:
            - name: "confirm_recap"
              value: True
        - action_executed: action_process_credit_card_purchase